<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { subscriptionStatus } from '$lib/stores/subscriptionStatus';
    import { SubscriptionService } from '$lib/api/features/subscription/subscription.service';
    import type { LayoutData } from './$types';
    
    export let data: LayoutData;  // Get data from layout

    const subscriptionService = new SubscriptionService();
    let hasCheckedSubscription = false;
    
    // Debug
	// $: console.log('(site)/+page.svelte: Layout data:', data);
    // $: console.log('(site)/+page.svelte: Subscription:', $subscriptionStatus);

    // Check subscription status when we have a token
    $: if (!hasCheckedSubscription) {
        const accessToken = data.access_token;

        if (accessToken) {
            hasCheckedSubscription = true;
            subscriptionStatus.setLoading(true);

            subscriptionService.getSubscriptionStatus(accessToken)
                .then(response => {
                    if (response.res_status === 200 && response.data) {
                        subscriptionStatus.setStatus(
                            response.data.is_active,
                            response.data.expires_at
                        );
                    } else {
                        subscriptionStatus.setError(
                            response.error_msg || 'Failed to fetch subscription status'
                        );
                    }
                })
                .catch(error => {
                    console.error('Error checking subscription status:', error);
                    subscriptionStatus.setError('Network error while checking subscription status');
                });
        } else {
            // No access token found, user is not logged in
            goto('/');
        }
    }

    // Navigate based on subscription status
    $: if ($subscriptionStatus.checked) {
        if ($subscriptionStatus.is_active) {
            goto('/chat_center');
        } else {
            goto('/subscription');
        }
    }
</script>
